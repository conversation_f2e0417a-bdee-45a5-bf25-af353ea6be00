#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件路径提取器
提取指定目录下所有文件的相对路径
"""

import os
import sys
from pathlib import Path


def extract_all_file_paths(root_directory="."):
    """
    提取指定目录下所有文件的相对路径
    
    Args:
        root_directory (str): 根目录路径，默认为当前目录
    
    Returns:
        list: 包含所有文件相对路径的列表
    """
    file_paths = []
    root_path = Path(root_directory).resolve()
    
    try:
        # 遍历目录树
        for root, dirs, files in os.walk(root_directory):
            for file in files:
                # 获取完整路径
                full_path = os.path.join(root, file)
                # 转换为相对路径
                relative_path = os.path.relpath(full_path, root_directory)
                file_paths.append(relative_path)
        
        return sorted(file_paths)
    
    except Exception as e:
        print(f"错误: 无法访问目录 '{root_directory}': {e}")
        return []


def save_paths_to_file(file_paths, output_file="file_paths.txt"):
    """
    将文件路径保存到文本文件
    
    Args:
        file_paths (list): 文件路径列表
        output_file (str): 输出文件名
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for path in file_paths:
                f.write(path + '\n')
        print(f"文件路径已保存到: {output_file}")
    except Exception as e:
        print(f"错误: 无法保存文件 '{output_file}': {e}")


def main():
    """主函数"""
    print("文件路径提取器")
    print("=" * 50)
    
    # 获取当前目录下的所有文件路径
    current_dir = "."
    print(f"正在扫描目录: {os.path.abspath(current_dir)}")
    
    file_paths = extract_all_file_paths(current_dir)
    
    if not file_paths:
        print("未找到任何文件")
        return
    
    print(f"\n找到 {len(file_paths)} 个文件:")
    print("-" * 50)
    
    # 显示所有文件路径
    for i, path in enumerate(file_paths, 1):
        print(f"{i:3d}. {path}")
    
    print("-" * 50)
    
    # 保存到文件
    save_paths_to_file(file_paths)
    
    # 按文件夹分组显示
    print("\n按文件夹分组:")
    print("-" * 50)
    
    folders = {}
    for path in file_paths:
        folder = os.path.dirname(path)
        if folder == "":
            folder = "根目录"
        
        if folder not in folders:
            folders[folder] = []
        folders[folder].append(os.path.basename(path))
    
    for folder, files in sorted(folders.items()):
        print(f"\n📁 {folder}:")
        for file in sorted(files):
            if folder == "根目录":
                print(f"   📄 {file}")
            else:
                print(f"   📄 {os.path.join(folder, file)}")


if __name__ == "__main__":
    main()
