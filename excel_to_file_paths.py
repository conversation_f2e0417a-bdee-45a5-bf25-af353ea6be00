#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件名提取与路径查找合并程序
1. 从Excel文件中提取所有工作表第一列的名称
2. 在指定目录中查找这些文件的路径
3. 生成TXT和CSV格式的结果文件
"""

import pandas as pd
import json
import os
import csv
import sys


def extract_first_column_names(excel_file):
    """
    提取Excel文件中所有工作表第一列的名称
    
    Args:
        excel_file (str): Excel文件路径
    
    Returns:
        dict: 包含每个工作表第一列名称的字典
    """
    try:
        # 读取Excel文件的所有工作表名称
        excel_file_obj = pd.ExcelFile(excel_file)
        sheet_names = excel_file_obj.sheet_names
        
        print(f"找到 {len(sheet_names)} 个工作表: {sheet_names}")
        
        first_column_data = {}
        
        for sheet_name in sheet_names:
            try:
                # 读取每个工作表
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                
                if not df.empty and len(df.columns) > 0:
                    # 获取第一列的所有非空值
                    first_column = df.iloc[:, 0].dropna().tolist()
                    first_column_data[sheet_name] = first_column
                    print(f"工作表 '{sheet_name}': 第一列有 {len(first_column)} 个非空值")
                else:
                    first_column_data[sheet_name] = []
                    print(f"工作表 '{sheet_name}': 为空或没有列")
                    
            except Exception as e:
                print(f"读取工作表 '{sheet_name}' 时出错: {e}")
                first_column_data[sheet_name] = []
        
        return first_column_data
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return {}


def save_to_json(data, output_file="excel_first_column_data.json"):
    """
    将所有工作表的第一列数据保存到一个JSON文件中
    
    Args:
        data (dict): 包含工作表和第一列数据的字典
        output_file (str): 输出JSON文件名
    """
    try:
        # 将数据保存为JSON格式
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"JSON文件已保存到: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"保存JSON文件时出错: {e}")
        return None


def extract_all_filenames(json_data):
    """
    从JSON数据中提取所有文件名
    
    Args:
        json_data (dict): JSON数据
    
    Returns:
        set: 所有唯一的文件名集合
    """
    all_filenames = set()
    
    for _, file_list in json_data.items():
        for filename in file_list:
            if filename and isinstance(filename, str):
                # 清理文件名，移除可能的路径分隔符
                clean_filename = os.path.basename(str(filename).strip())
                if clean_filename:
                    all_filenames.add(clean_filename)
    
    print(f"从JSON中提取到 {len(all_filenames)} 个唯一文件名")
    return all_filenames


def find_files_in_directory(root_directory, target_filenames):
    """
    在指定目录中查找目标文件（支持模糊匹配）
    
    Args:
        root_directory (str): 根目录路径
        target_filenames (set): 要查找的文件名集合
    
    Returns:
        dict: 文件名到相对路径的映射
    """
    found_files = {}
    not_found_files = set(target_filenames)
    
    print(f"正在搜索目录: {os.path.abspath(root_directory)}")
    
    try:
        # 遍历目录树
        for root, _, files in os.walk(root_directory):
            for file in files:
                # 获取文件名（不含扩展名）
                file_base = os.path.splitext(file)[0]
                
                # 检查是否匹配目标文件名
                for target_name in target_filenames:
                    # 精确匹配
                    if file == target_name or file_base == target_name:
                        match_found = True
                    # 模糊匹配：检查文件名是否以目标名称开头
                    elif file_base.startswith(target_name):
                        match_found = True
                    # 更宽松的匹配：移除常见后缀后比较
                    elif (file_base.replace('-翻译版', '').replace('-英文版', '').replace('-中文版', '') == target_name):
                        match_found = True
                    else:
                        match_found = False
                    
                    if match_found:
                        # 获取相对路径
                        full_path = os.path.join(root, file)
                        relative_path = os.path.relpath(full_path, root_directory)
                        
                        # 如果目标文件名已存在，保存为列表
                        if target_name in found_files:
                            if isinstance(found_files[target_name], list):
                                found_files[target_name].append(relative_path)
                            else:
                                found_files[target_name] = [found_files[target_name], relative_path]
                        else:
                            found_files[target_name] = relative_path
                        
                        # 从未找到列表中移除
                        not_found_files.discard(target_name)
                        break  # 找到匹配后跳出内层循环
    
    except Exception as e:
        print(f"搜索文件时出错: {e}")
    
    print(f"找到 {len(found_files)} 个文件")
    if not_found_files:
        print(f"未找到 {len(not_found_files)} 个文件")
    
    return found_files, not_found_files


def save_results_to_txt(found_files, json_data, output_file="file_paths_result.txt"):
    """
    将查找结果保存到txt文件（简洁格式）
    
    Args:
        found_files (dict): 找到的文件映射
        json_data (dict): 原始JSON数据
        output_file (str): 输出文件名
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            # 按工作表分组显示，简洁格式
            for sheet_name, file_list in json_data.items():
                f.write(f"{sheet_name}\n")
                
                for filename in file_list:
                    if filename and isinstance(filename, str):
                        clean_filename = os.path.basename(str(filename).strip())
                        if clean_filename in found_files:
                            paths = found_files[clean_filename]
                            if isinstance(paths, list):
                                for path in paths:
                                    f.write(f"{path}\n")
                            else:
                                f.write(f"{paths}\n")
                
                f.write("\n")
        
        print(f"TXT文件已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存TXT文件时出错: {e}")


def save_results_to_csv(found_files, json_data, output_file="file_paths_result.csv"):
    """
    将查找结果保存到CSV文件
    
    Args:
        found_files (dict): 找到的文件映射
        json_data (dict): 原始JSON数据
        output_file (str): 输出CSV文件名
    """
    try:
        with open(output_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            # 写入表头
            writer.writerow(['工作表名称', '文件名', '文件路径'])
            
            # 按工作表分组写入数据
            for sheet_name, file_list in json_data.items():
                for filename in file_list:
                    if filename and isinstance(filename, str):
                        clean_filename = os.path.basename(str(filename).strip())
                        if clean_filename in found_files:
                            paths = found_files[clean_filename]
                            if isinstance(paths, list):
                                for path in paths:
                                    writer.writerow([sheet_name, clean_filename, path])
                            else:
                                writer.writerow([sheet_name, clean_filename, paths])
        
        print(f"CSV文件已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存CSV文件时出错: {e}")


def main():
    """主函数"""
    print("Excel文件名提取与路径查找程序")
    print("=" * 60)
    
    # 第一步：从Excel文件提取数据
    excel_file = "其它.xlsx"
    search_directory = "."
    
    # 检查Excel文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误: 找不到Excel文件 '{excel_file}'")
        return
    
    print(f"第一步：从Excel文件提取数据")
    print(f"正在处理文件: {excel_file}")
    
    # 提取第一列数据
    first_column_data = extract_first_column_names(excel_file)
    
    if not first_column_data:
        print("未能提取到任何数据")
        return
    
    # 保存到JSON文件
    json_file = save_to_json(first_column_data)
    if not json_file:
        print("JSON文件保存失败")
        return
    
    print(f"\n第二步：在目录中查找文件")
    print(f"搜索目录: {os.path.abspath(search_directory)}")
    
    # 提取所有文件名
    target_filenames = extract_all_filenames(first_column_data)
    if not target_filenames:
        print("JSON中没有找到有效的文件名")
        return
    
    # 在目录中查找文件
    found_files, not_found_files = find_files_in_directory(search_directory, target_filenames)
    
    # 显示查找结果概览
    print(f"\n查找结果概览:")
    print(f"- 找到的文件: {len(found_files)}")
    print(f"- 未找到的文件: {len(not_found_files)}")
    
    print(f"\n第三步：保存结果文件")
    
    # 保存结果到TXT和CSV文件
    save_results_to_txt(found_files, first_column_data)
    save_results_to_csv(found_files, first_column_data)
    
    print(f"\n程序执行完成！")
    print(f"生成的文件:")
    print(f"- {json_file}")
    print(f"- file_paths_result.txt")
    print(f"- file_paths_result.csv")


if __name__ == "__main__":
    main()
