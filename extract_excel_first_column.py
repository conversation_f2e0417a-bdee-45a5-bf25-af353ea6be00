#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel第一列提取器
提取Excel文件中所有工作表第一列的名称
"""

import pandas as pd
import sys
import os
from pathlib import Path


def extract_first_column_names(excel_file):
    """
    提取Excel文件中所有工作表第一列的名称
    
    Args:
        excel_file (str): Excel文件路径
    
    Returns:
        dict: 包含每个工作表第一列名称的字典
    """
    try:
        # 读取Excel文件的所有工作表名称
        excel_file_obj = pd.ExcelFile(excel_file)
        sheet_names = excel_file_obj.sheet_names
        
        print(f"找到 {len(sheet_names)} 个工作表: {sheet_names}")
        
        first_column_data = {}
        
        for sheet_name in sheet_names:
            try:
                # 读取每个工作表
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                
                if not df.empty and len(df.columns) > 0:
                    # 获取第一列的所有非空值
                    first_column = df.iloc[:, 0].dropna().tolist()
                    first_column_data[sheet_name] = first_column
                    print(f"工作表 '{sheet_name}': 第一列有 {len(first_column)} 个非空值")
                else:
                    first_column_data[sheet_name] = []
                    print(f"工作表 '{sheet_name}': 为空或没有列")
                    
            except Exception as e:
                print(f"读取工作表 '{sheet_name}' 时出错: {e}")
                first_column_data[sheet_name] = []
        
        return first_column_data
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return {}


def save_sheets_to_separate_files(data, output_folder="excel_sheets_output"):
    """
    将每个工作表的第一列数据保存到单独的txt文件中

    Args:
        data (dict): 包含工作表和第一列数据的字典
        output_folder (str): 输出文件夹名称
    """
    try:
        # 创建输出文件夹
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
            print(f"创建文件夹: {output_folder}")

        saved_files = []

        for sheet_name, column_data in data.items():
            # 清理工作表名称，移除不能用作文件名的字符
            safe_sheet_name = "".join(c for c in sheet_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_sheet_name = safe_sheet_name.replace(' ', '_')

            output_file = os.path.join(output_folder, f"{safe_sheet_name}.txt")

            with open(output_file, 'w', encoding='utf-8') as f:
                # 只写入第一列的数据，不添加任何修饰文本
                for item in column_data:
                    f.write(f"{item}\n")

            saved_files.append(output_file)
            print(f"保存工作表 '{sheet_name}' 到: {output_file} ({len(column_data)} 项)")

        print(f"\n所有文件已保存到文件夹: {output_folder}")
        print(f"共生成 {len(saved_files)} 个文件")

        return saved_files

    except Exception as e:
        print(f"保存文件时出错: {e}")
        return []


def main():
    """主函数"""
    excel_file = "其它.xlsx"
    
    print("Excel第一列提取器")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误: 找不到文件 '{excel_file}'")
        return
    
    print(f"正在处理文件: {excel_file}")
    
    # 提取第一列数据
    first_column_data = extract_first_column_names(excel_file)
    
    if not first_column_data:
        print("未能提取到任何数据")
        return
    
    print("\n提取结果:")
    print("-" * 50)
    
    # 显示结果
    for sheet_name, column_data in first_column_data.items():
        print(f"\n📊 工作表: {sheet_name}")
        if column_data:
            for i, item in enumerate(column_data[:10], 1):  # 只显示前10个
                print(f"   {i:2d}. {item}")
            if len(column_data) > 10:
                print(f"   ... (还有 {len(column_data) - 10} 个)")
        else:
            print("   (无数据)")
    
    # 保存到单独的文件
    saved_files = save_sheets_to_separate_files(first_column_data)
    
    # 统计信息
    total_items = sum(len(data) for data in first_column_data.values())
    print(f"\n统计信息:")
    print(f"- 工作表数量: {len(first_column_data)}")
    print(f"- 第一列总项目数: {total_items}")


if __name__ == "__main__":
    main()
