#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据JSON文件查找文件路径
读取JSON文件中的文件名，在根目录中查找对应文件的相对路径
"""

import json
import os
import sys
from pathlib import Path


def load_json_data(json_file):
    """
    加载JSON文件数据
    
    Args:
        json_file (str): JSON文件路径
    
    Returns:
        dict: JSON数据
    """
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载JSON文件: {json_file}")
        return data
    except Exception as e:
        print(f"加载JSON文件时出错: {e}")
        return {}


def extract_all_filenames(json_data):
    """
    从JSON数据中提取所有文件名
    
    Args:
        json_data (dict): JSON数据
    
    Returns:
        set: 所有唯一的文件名集合
    """
    all_filenames = set()
    
    for sheet_name, file_list in json_data.items():
        for filename in file_list:
            if filename and isinstance(filename, str):
                # 清理文件名，移除可能的路径分隔符
                clean_filename = os.path.basename(str(filename).strip())
                if clean_filename:
                    all_filenames.add(clean_filename)
    
    print(f"从JSON中提取到 {len(all_filenames)} 个唯一文件名")
    return all_filenames


def find_files_in_directory(root_directory, target_filenames):
    """
    在指定目录中查找目标文件（支持模糊匹配）

    Args:
        root_directory (str): 根目录路径
        target_filenames (set): 要查找的文件名集合

    Returns:
        dict: 文件名到相对路径的映射
    """
    found_files = {}
    not_found_files = set(target_filenames)

    print(f"正在搜索目录: {os.path.abspath(root_directory)}")

    try:
        # 遍历目录树
        for root, dirs, files in os.walk(root_directory):
            for file in files:
                # 获取文件名（不含扩展名）
                file_base = os.path.splitext(file)[0]

                # 检查是否匹配目标文件名
                for target_name in target_filenames:
                    # 精确匹配
                    if file == target_name or file_base == target_name:
                        match_found = True
                    # 模糊匹配：检查文件名是否以目标名称开头
                    elif file_base.startswith(target_name):
                        match_found = True
                    # 更宽松的匹配：移除常见后缀后比较
                    elif (file_base.replace('-翻译版', '').replace('-英文版', '').replace('-中文版', '') == target_name):
                        match_found = True
                    else:
                        match_found = False

                    if match_found:
                        # 获取相对路径
                        full_path = os.path.join(root, file)
                        relative_path = os.path.relpath(full_path, root_directory)

                        # 如果目标文件名已存在，保存为列表
                        if target_name in found_files:
                            if isinstance(found_files[target_name], list):
                                found_files[target_name].append(relative_path)
                            else:
                                found_files[target_name] = [found_files[target_name], relative_path]
                        else:
                            found_files[target_name] = relative_path

                        # 从未找到列表中移除
                        not_found_files.discard(target_name)
                        break  # 找到匹配后跳出内层循环

    except Exception as e:
        print(f"搜索文件时出错: {e}")

    print(f"找到 {len(found_files)} 个文件")
    if not_found_files:
        print(f"未找到 {len(not_found_files)} 个文件")

    return found_files, not_found_files


def save_results_to_txt(found_files, not_found_files, json_data, output_file="file_paths_from_json.txt"):
    """
    将查找结果保存到txt文件
    
    Args:
        found_files (dict): 找到的文件映射
        not_found_files (set): 未找到的文件集合
        json_data (dict): 原始JSON数据
        output_file (str): 输出文件名
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("根据JSON文件查找的文件路径\n")
            f.write("=" * 50 + "\n\n")
            
            # 按工作表分组显示
            f.write("按工作表分组的文件路径:\n")
            f.write("-" * 30 + "\n\n")
            
            for sheet_name, file_list in json_data.items():
                f.write(f"工作表: {sheet_name}\n")
                f.write("~" * 20 + "\n")
                
                for filename in file_list:
                    if filename and isinstance(filename, str):
                        clean_filename = os.path.basename(str(filename).strip())
                        if clean_filename in found_files:
                            paths = found_files[clean_filename]
                            if isinstance(paths, list):
                                for path in paths:
                                    f.write(f"{path}\n")
                            else:
                                f.write(f"{paths}\n")
                        else:
                            f.write(f"[未找到] {clean_filename}\n")
                
                f.write("\n")
            
            # 所有找到的文件路径
            f.write("=" * 50 + "\n")
            f.write("所有找到的文件路径:\n")
            f.write("=" * 50 + "\n")
            
            all_paths = []
            for filename, paths in found_files.items():
                if isinstance(paths, list):
                    all_paths.extend(paths)
                else:
                    all_paths.append(paths)
            
            for path in sorted(all_paths):
                f.write(f"{path}\n")
            
            # 未找到的文件
            if not_found_files:
                f.write("\n" + "=" * 50 + "\n")
                f.write("未找到的文件:\n")
                f.write("=" * 50 + "\n")
                
                for filename in sorted(not_found_files):
                    f.write(f"{filename}\n")
        
        print(f"结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存结果时出错: {e}")


def main():
    """主函数"""
    json_file = "excel_first_column_data.json"
    root_directory = "."
    
    print("根据JSON文件查找文件路径")
    print("=" * 50)
    
    # 检查JSON文件是否存在
    if not os.path.exists(json_file):
        print(f"错误: 找不到JSON文件 '{json_file}'")
        return
    
    # 加载JSON数据
    json_data = load_json_data(json_file)
    if not json_data:
        print("JSON文件为空或加载失败")
        return
    
    # 提取所有文件名
    target_filenames = extract_all_filenames(json_data)
    if not target_filenames:
        print("JSON中没有找到有效的文件名")
        return
    
    print(f"\n要查找的文件名示例:")
    for i, filename in enumerate(sorted(target_filenames)[:10], 1):
        print(f"  {i}. {filename}")
    if len(target_filenames) > 10:
        print(f"  ... (还有 {len(target_filenames) - 10} 个)")
    
    # 在目录中查找文件
    print(f"\n开始在目录中查找文件...")
    found_files, not_found_files = find_files_in_directory(root_directory, target_filenames)
    
    # 显示查找结果概览
    print(f"\n查找结果概览:")
    print(f"- 找到的文件: {len(found_files)}")
    print(f"- 未找到的文件: {len(not_found_files)}")
    
    # 保存结果
    save_results_to_txt(found_files, not_found_files, json_data)
    
    # 显示一些找到的文件示例
    if found_files:
        print(f"\n找到的文件示例:")
        count = 0
        for filename, paths in found_files.items():
            if count >= 5:
                break
            if isinstance(paths, list):
                for path in paths[:2]:  # 最多显示2个路径
                    print(f"  {path}")
            else:
                print(f"  {paths}")
            count += 1


if __name__ == "__main__":
    main()
