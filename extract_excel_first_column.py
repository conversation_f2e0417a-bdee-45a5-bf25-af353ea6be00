#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel第一列提取器
提取Excel文件中所有工作表第一列的名称
"""

import pandas as pd
import sys
import os
from pathlib import Path


def extract_first_column_names(excel_file):
    """
    提取Excel文件中所有工作表第一列的名称
    
    Args:
        excel_file (str): Excel文件路径
    
    Returns:
        dict: 包含每个工作表第一列名称的字典
    """
    try:
        # 读取Excel文件的所有工作表名称
        excel_file_obj = pd.ExcelFile(excel_file)
        sheet_names = excel_file_obj.sheet_names
        
        print(f"找到 {len(sheet_names)} 个工作表: {sheet_names}")
        
        first_column_data = {}
        
        for sheet_name in sheet_names:
            try:
                # 读取每个工作表
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                
                if not df.empty and len(df.columns) > 0:
                    # 获取第一列的所有非空值
                    first_column = df.iloc[:, 0].dropna().tolist()
                    first_column_data[sheet_name] = first_column
                    print(f"工作表 '{sheet_name}': 第一列有 {len(first_column)} 个非空值")
                else:
                    first_column_data[sheet_name] = []
                    print(f"工作表 '{sheet_name}': 为空或没有列")
                    
            except Exception as e:
                print(f"读取工作表 '{sheet_name}' 时出错: {e}")
                first_column_data[sheet_name] = []
        
        return first_column_data
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return {}


def save_to_txt(data, output_file="excel_first_column_names.txt"):
    """
    将提取的数据保存到txt文件
    
    Args:
        data (dict): 包含工作表和第一列数据的字典
        output_file (str): 输出文件名
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("Excel文件所有工作表第一列名称\n")
            f.write("=" * 50 + "\n\n")
            
            for sheet_name, column_data in data.items():
                f.write(f"工作表: {sheet_name}\n")
                f.write("-" * 30 + "\n")
                
                if column_data:
                    for i, item in enumerate(column_data, 1):
                        f.write(f"{i:3d}. {item}\n")
                else:
                    f.write("(无数据)\n")
                
                f.write("\n")
            
            # 汇总所有第一列的名称
            f.write("=" * 50 + "\n")
            f.write("所有第一列名称汇总:\n")
            f.write("=" * 50 + "\n")
            
            all_names = []
            for sheet_name, column_data in data.items():
                all_names.extend(column_data)
            
            # 去重并排序
            unique_names = sorted(list(set(all_names)))
            
            for i, name in enumerate(unique_names, 1):
                f.write(f"{i:3d}. {name}\n")
        
        print(f"结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存文件时出错: {e}")


def main():
    """主函数"""
    excel_file = "其它.xlsx"
    
    print("Excel第一列提取器")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误: 找不到文件 '{excel_file}'")
        return
    
    print(f"正在处理文件: {excel_file}")
    
    # 提取第一列数据
    first_column_data = extract_first_column_names(excel_file)
    
    if not first_column_data:
        print("未能提取到任何数据")
        return
    
    print("\n提取结果:")
    print("-" * 50)
    
    # 显示结果
    for sheet_name, column_data in first_column_data.items():
        print(f"\n📊 工作表: {sheet_name}")
        if column_data:
            for i, item in enumerate(column_data[:10], 1):  # 只显示前10个
                print(f"   {i:2d}. {item}")
            if len(column_data) > 10:
                print(f"   ... (还有 {len(column_data) - 10} 个)")
        else:
            print("   (无数据)")
    
    # 保存到文件
    save_to_txt(first_column_data)
    
    # 统计信息
    total_items = sum(len(data) for data in first_column_data.values())
    print(f"\n统计信息:")
    print(f"- 工作表数量: {len(first_column_data)}")
    print(f"- 第一列总项目数: {total_items}")


if __name__ == "__main__":
    main()
